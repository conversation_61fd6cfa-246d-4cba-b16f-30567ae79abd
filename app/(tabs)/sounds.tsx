import { Image, StyleSheet, Platform, SafeAreaView, View, FlatList, Text, Pressable } from 'react-native';

import { ThemedText } from '@/components/ThemedText';
import { MAIN_STYLES } from '@/constants/Styles';
import { Colors } from '@/constants/Colors';
import Ionicons from '@expo/vector-icons/Ionicons';
import Feather from '@expo/vector-icons/Feather';
import { FontAwesome5 } from '@expo/vector-icons';
import { useEffect, useState, memo, useCallback } from 'react';
import { useAudioPlayer } from 'expo-audio';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { getTranslation } from '@/components/util/LocalizationUtil';
import * as Haptics from 'expo-haptics';

// TODO: Do attribution for the sounds
// These ones are got from soundbible.com
const SOUNDS = [
  {
    id: 0,
    title: getTranslation('natureSounds'),
    subTitle: getTranslation('natureSoundsDescription'),
    sounds: [
      {
        id: 0,
        title: getTranslation('rain'),
        url: "https://storyai-files.dofatech.com/sound_files/rain.mp3",
        icon: <Ionicons name="rainy" size={24} color={Colors['light'].text} />,
        glowColor: '#87CEFA'  // Sky blue for rain
      },
      {
        id: 1,
        title: getTranslation('wind'),
        url: "https://storyai-files.dofatech.com/sound_files/wind.mp3",
        icon: <Feather name="wind" size={24} color={Colors['light'].text} />,
        glowColor: '#B19CD9'  // Lavender for wind
      },
      {
        id: 2,
        title: getTranslation('forest'),
        url: "https://storyai-files.dofatech.com/sound_files/forest.mp3",
        icon: <FontAwesome5 name="tree" size={24} color={Colors['light'].text} />,
        glowColor: '#7FFF8E'  // Bright green for forest
      },
      {
        id: 3,
        title: getTranslation('lake'),
        url: "https://storyai-files.dofatech.com/sound_files/lake.mp3",
        icon: <FontAwesome5 name="water" size={24} color={Colors['light'].text} />,
        glowColor: '#00FFB3'  // Turquoise for lake
      }
    ]
  },
  {
    id: 1,
    title: getTranslation('animalSounds'),
    subTitle: getTranslation('animalSoundsDescription'),
    sounds: [
      {
        id: 4,
        title: getTranslation('birds'),
        url: "https://storyai-files.dofatech.com/sound_files/birds.mp3",
        icon: <MaterialCommunityIcons name="bird" size={24} color={Colors['light'].text} />,
        glowColor: '#FFEF00'  // Bright yellow for birds
      },
      {
        id: 5,
        title: getTranslation('frogs'),
        url: "https://storyai-files.dofatech.com/sound_files/frog.mp3",
        icon: <FontAwesome5 name="frog" size={24} color={Colors['light'].text} />,
        glowColor: '#7FFF8E'  // Bright green for frogs
      },
      {
        id: 6,
        title: getTranslation('sheep'),
        url: "https://storyai-files.dofatech.com/sound_files/sheep.mp3",
        icon: <MaterialCommunityIcons name="sheep" size={24} color={Colors['light'].text} />,
        glowColor: '#FFB6C1'  // Light pink for sheep
      }
    ]
  }
]

const SoundItem = memo(({ sound, onPress, isSelected }) => {
  return (
    <Pressable
      style={[
        styles.soundItemContainer,
        isSelected && {
          borderColor: sound.glowColor,
          borderWidth: 2,
          shadowColor: sound.glowColor,
          shadowOffset: { width: 0, height: 0 },
          shadowOpacity: 0.8,
          shadowRadius: 4,
          elevation: 5,
        }
      ]}
      onPress={() => onPress(sound.url, sound.id)}>
      <View style={MAIN_STYLES.flex1} />
      {sound.icon}
      <View style={MAIN_STYLES.flex1} />
      <View style={styles.soundItemInnerContainer}>
        <Text style={styles.soundItemTitle}>{sound.title}</Text>
      </View>
    </Pressable>
  )
});

const SoundRow = memo(({ category, onSoundPress, selectedSoundId }) => {
  const renderItem = useCallback(({ item }) => (
    <SoundItem 
      sound={item} 
      onPress={onSoundPress} 
      isSelected={selectedSoundId === item.id}
    />
  ), [onSoundPress, selectedSoundId]);

  return (
    <View style={styles.soundCategoryRow}>
      <ThemedText type="h3" style={styles.soundCategoryTitle}>{category.title}</ThemedText>
      <Text style={styles.soundCategorySubtitle}>{category.subTitle}</Text>

      <FlatList
        data={category.sounds}
        contentContainerStyle={styles.soundItemFlatListInnerContainer}
        renderItem={renderItem}
        horizontal={true}
        showsHorizontalScrollIndicator={false}
        removeClippedSubviews={false}
        keyExtractor={(item) => `list_item_${item.id.toString()}`}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
        }}
      />
    </View>
  )
});

export default function SoundsScreen() {
  const [selectedSoundId, setSelectedSoundId] = useState(-1);
  const [currentUrl, setCurrentUrl] = useState<string | null>(null);
  const player = useAudioPlayer(currentUrl, { loop: true });

  const handleSoundPress = useCallback(async (url: string, id: number) => {
    if (selectedSoundId === id && player.playing) {
      player.pause();
      setSelectedSoundId(-1);
      setCurrentUrl(null);
      return;
    }

    setSelectedSoundId(id);
    console.log('Loading Sound');

    setCurrentUrl(url);
    Haptics.selectionAsync();

    console.log('Playing Sound');
    player.play();
  }, [selectedSoundId, player]);

  const renderSoundRow = useCallback(({ item }) => (
    <SoundRow 
      category={item} 
      onSoundPress={handleSoundPress}
      selectedSoundId={selectedSoundId}
    />
  ), [handleSoundPress, selectedSoundId]);

  useEffect(() => {
    return () => {
      if (player.playing) {
        console.log('Stopping Sound');
        player.pause();
      }
    };
  }, [player]);

  return (
    <SafeAreaView style={MAIN_STYLES.safeArea}>
      <View style={[MAIN_STYLES.mainView, { paddingHorizontal: 0, paddingBottom: 0 }]}>
        <ThemedText type="h1" style={{ marginLeft: 16 }}>{getTranslation('sounds')}</ThemedText>
        <FlatList
          data={SOUNDS}
          renderItem={renderSoundRow}
          keyExtractor={(item) => `list_${item.id.toString()}`}
          maintainVisibleContentPosition={{
            minIndexForVisible: 0,
          }}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  soundCategoryRow: {
    marginBottom: 24
  },
  soundCategoryTitle: {
    marginLeft: 16,
    marginBottom: 4
  },
  soundCategorySubtitle: {
    fontSize: 16,
    marginLeft: 16,
    color: '#EBEBF599',
    marginBottom: 8
  },
  soundItemFlatListInnerContainer: {
    paddingLeft: 16
  },
  soundItemContainer: {
    borderRadius: 16,
    overflow: Platform.OS === 'android' ? 'hidden' : 'visible',
    marginRight: 16,
    height: 125,
    width: 100,
    backgroundColor: '#21283F',
    alignItems: 'center',
    justifyContent: 'center'
  },
  soundItemInnerContainer: {
    backgroundColor: '#2D344B',
    padding: 8,
    width: '100%',
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16
  },
  soundItemTitle: {
    fontSize: 12,
    fontWeight: '400',
    color: Colors['light'].text,
    textAlign: 'center'
  }
});
